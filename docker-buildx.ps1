# 多架构构建脚本 (PowerShell版本)
# 使用方法: .\docker-buildx.ps1 [version]
# 示例: .\docker-buildx.ps1 v1.0.0

param(
    [string]$Version = "latest"
)

# 配置
$DOCKER_USERNAME = "curaalizm"
$IMAGE_NAME = "gemini-antiblock"

Write-Host "🚀 开始构建和发布多架构 Docker 镜像..." -ForegroundColor Green
Write-Host "用户名: $DOCKER_USERNAME"
Write-Host "镜像名: $IMAGE_NAME"
Write-Host "版本: $Version"
Write-Host ""

# 检查是否安装了Docker Buildx
try {
    $buildxVersion = docker buildx version
    Write-Host "✅ Docker Buildx 已安装: $buildxVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 未安装 Docker Buildx" -ForegroundColor Red
    Write-Host "请先安装 Docker Buildx: https://github.com/docker/buildx#installing"
    exit 1
}

# 检查Docker是否运行
try {
    docker info > $null
} catch {
    Write-Host "❌ Docker 未运行，请先启动 Docker Desktop" -ForegroundColor Red
    exit 1
}

# 创建并使用构建器实例
Write-Host "🔨 创建构建器实例..." -ForegroundColor Yellow
$BUILDER_NAME = "mybuilder-$(Get-Date -UFormat %s)"
docker buildx create --name $BUILDER_NAME --use
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 构建器实例创建失败" -ForegroundColor Red
    exit 1
}

# 确保构建器启动
docker buildx inspect --bootstrap > $null
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 构建器启动失败" -ForegroundColor Red
    docker buildx rm $BUILDER_NAME
    exit 1
}

# 构建并推送多架构镜像
Write-Host "🏗️  构建并推送多架构镜像..." -ForegroundColor Yellow
docker buildx build --platform linux/amd64,linux/arm64,linux/arm/v7 `
    -t "${DOCKER_USERNAME}/${IMAGE_NAME}:${Version}" `
    -t "${DOCKER_USERNAME}/${IMAGE_NAME}:latest" `
    . --push

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 镜像构建或推送失败" -ForegroundColor Red
    docker buildx rm $BUILDER_NAME
    exit 1
}

# 清理构建器实例
Write-Host "🧹 清理构建器实例..." -ForegroundColor Yellow
docker buildx rm $BUILDER_NAME

Write-Host ""
Write-Host "✅ 多架构镜像发布完成！" -ForegroundColor Green
Write-Host "镜像地址: https://hub.docker.com/r/${DOCKER_USERNAME}/${IMAGE_NAME}"
Write-Host "支持架构: linux/amd64, linux/arm64, linux/arm/v7"
Write-Host "拉取命令: docker pull ${DOCKER_USERNAME}/${IMAGE_NAME}:${Version}"