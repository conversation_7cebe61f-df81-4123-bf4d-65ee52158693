// main.go
// 翻译自给定的 Cloudflare Worker（JS）到 Go 版本的可运行服务。
// 功能：作为 Gemini API 的代理，支持 SSE 流式转发、智能重试、错误标准化、文本清洗、CORS。
// 环境变量（均有默认值）：
//   PORT=8080
//   UPSTREAM_URL_BASE=https://api-proxy.me/gemini
//   MAX_CONSECUTIVE_RETRIES=10
//   DEBUG_MODE=true
//   RETRY_DELAY_MS=750
//   LOG_TRUNCATION_LIMIT=8000
//
// 说明：为保持行为一致，保留了 JS 版本中的“智能退避策略”“内容问题检测”等逻辑。
// 仅使用标准库。

package main

import (
	"bufio"
	"bytes"
	//"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"math"
	"net/http"
	"net/textproto"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"
	//"unicode"
	"unicode/utf8"
)

// -------------------- 配置与日志 --------------------

type Config struct {
	Port                  string
	UpstreamURLBase       string
	MaxConsecutiveRetries int
	DebugMode             bool
	RetryDelayMs          int
	LogTruncationLimit    int

	// 下面几个参数未暴露为环境变量，采用与原脚本接近的默认值
	MaxRetryDelayMs     int
	NoProgressThreshold int
	MinProgressChars    int
}

var CONFIG = Config{
	Port:                  envOrDefault("PORT", "8080"),
	UpstreamURLBase:       envOrDefault("UPSTREAM_URL_BASE", "https://api-proxy.me/gemini"),
	MaxConsecutiveRetries: envOrDefaultInt("MAX_CONSECUTIVE_RETRIES", 10),
	DebugMode:             envOrDefaultBool("DEBUG_MODE", true),
	RetryDelayMs:          envOrDefaultInt("RETRY_DELAY_MS", 750),
	LogTruncationLimit:    envOrDefaultInt("LOG_TRUNCATION_LIMIT", 8000),

	MaxRetryDelayMs:     5000,
	NoProgressThreshold: 3,
	MinProgressChars:    100,
}

func envOrDefault(key, def string) string {
	if v := os.Getenv(key); v != "" {
		return v
	}
	return def
}
func envOrDefaultInt(key string, def int) int {
	if v := os.Getenv(key); v != "" {
		if i, err := strconv.Atoi(v); err == nil {
			return i
		}
	}
	return def
}
func envOrDefaultBool(key string, def bool) bool {
	if v := os.Getenv(key); v != "" {
		switch strings.ToLower(v) {
		case "1", "t", "true", "yes", "y":
			return true
		case "0", "f", "false", "no", "n":
			return false
		}
	}
	return def
}

func nowISO() string { return time.Now().UTC().Format(time.RFC3339) }

func logDebug(format string, v ...any) {
	if CONFIG.DebugMode {
		log.Printf("[DEBUG %s] %s", nowISO(), fmt.Sprintf(format, v...))
	}
}
func logInfo(format string, v ...any)  { log.Printf("[INFO %s] %s", nowISO(), fmt.Sprintf(format, v...)) }
func logWarn(format string, v ...any)  { log.Printf("[WARN %s] %s", nowISO(), fmt.Sprintf(format, v...)) }
func logError(format string, v ...any) { log.Printf("[ERROR %s] %s", nowISO(), fmt.Sprintf(format, v...)) }

func truncate(s string, n int) string {
	if n <= 0 {
		return s
	}
	if len(s) > n {
		return s[:n] + fmt.Sprintf("... [truncated %d chars]", len(s)-n)
	}
	return s
}

// -------------------- 工具函数：文本清洗 / JSON 安全处理 --------------------

// 移除控制字符（保留 \n \r \t），移除零宽字符，移除 U+2028/U+2029，修复无效 UTF-8。
func sanitizeTextForJSON(in string) string {
	if in == "" {
		return ""
	}
	// 修正无效 UTF-8（替换为有效序列）
	if !utf8.ValidString(in) {
		b := []byte(in)
		out := make([]rune, 0, len(b))
		for len(b) > 0 {
			r, size := utf8.DecodeRune(b)
			if r == utf8.RuneError && size == 1 {
				// 跳过无效字节
				b = b[1:]
				continue
			}
			out = append(out, r)
			b = b[size:]
		}
		in = string(out)
	}

	var buf strings.Builder
	buf.Grow(len(in))
	for _, r := range in {
		// 1) 过滤控制字符，但保留 \n \r \t
		if r < 0x20 {
			if r == '\n' || r == '\r' || r == '\t' {
				buf.WriteRune(r)
			}
			continue
		}
		// 2) 移除 DEL
		if r == 0x7F {
			continue
		}
		// 3) 移除零宽字符
		if (r >= 0x200B && r <= 0x200D) || r == 0xFEFF {
			continue
		}
		// 4) 移除段/行分隔符
		if r == 0x2028 || r == 0x2029 {
			continue
		}
		// 5) 过滤孤立代理对（在 UTF-8 中一般不会出现，但以防万一）
		//if unicode.Is(unicode.Surrogates, r) {
		//	continue
		//}
		buf.WriteRune(r)
	}
	clean := buf.String()
	logDebug("Text sanitized. Original length: %d, cleaned length: %d", len(in), len(clean))
	return clean
}

// 尝试解析 JSON；失败则清洗后再试。
func safeJSONParse(text string, v any) error {
	if strings.HasPrefix(text, "\uFEFF") { // 去除 BOM
		text = strings.TrimPrefix(text, "\uFEFF")
	}
	if err := json.Unmarshal([]byte(text), v); err == nil {
		return nil
	}
	logDebug("Initial JSON parse failed; trying after cleaning")
	clean := sanitizeTextForJSON(text)
	return json.Unmarshal([]byte(clean), v)
}

// 深度清洗：遍历对象中所有字符串并 sanitize；同时完成深拷贝。
func deepCleanClone(v any) any {
	switch x := v.(type) {
	case map[string]any:
		m2 := make(map[string]any, len(x))
		for k, val := range x {
			m2[k] = deepCleanClone(val)
		}
		return m2
	case []any:
		a2 := make([]any, len(x))
		for i := range x {
			a2[i] = deepCleanClone(x[i])
		}
		return a2
	case string:
		return sanitizeTextForJSON(x)
	case json.Number, float64, float32, int, int64, int32, bool, nil:
		return x
	default:
		// 尝试通过 JSON 循环一遍（尽力而为）
		b, _ := json.Marshal(x)
		var anyv any
		if err := json.Unmarshal(b, &anyv); err == nil {
			return deepCleanClone(anyv)
		}
		return x
	}
}

// -------------------- 错误标准化 --------------------

type StdError struct {
	Error StdErrorInner `json:"error"`
}
type StdErrorInner struct {
	Code    int           `json:"code"`
	Message string        `json:"message"`
	Status  string        `json:"status"`
	Details []any         `json:"details,omitempty"`
}

func statusToGoogleStatus(code int) string {
	switch code {
	case 400:
		return "INVALID_ARGUMENT"
	case 401:
		return "UNAUTHENTICATED"
	case 403:
		return "PERMISSION_DENIED"
	case 404:
		return "NOT_FOUND"
	case 429:
		return "RESOURCE_EXHAUSTED"
	case 500:
		return "INTERNAL"
	case 503:
		return "UNAVAILABLE"
	case 504:
		return "DEADLINE_EXCEEDED"
	default:
		return "UNKNOWN"
	}
}

func jsonError(w http.ResponseWriter, status int, message string, details any) {
	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.WriteHeader(status)
	st := StdError{
		Error: StdErrorInner{
			Code:    status,
			Message: message,
			Status:  statusToGoogleStatus(status),
		},
	}
	if details != nil {
		st.Error.Details = []any{details}
	}
	_ = json.NewEncoder(w).Encode(st)
}

var nonRetryableStatuses = map[int]struct{}{
	400: {}, 401: {}, 403: {}, 404: {}, 429: {},
}

func standardizeInitialError(resp *http.Response) (*StdError, string) {
	bodyBytes, _ := io.ReadAll(resp.Body)
	_ = resp.Body.Close()
	bodyText := string(bodyBytes)
	logError("Upstream error body: %s", truncate(bodyText, CONFIG.LogTruncationLimit))

	// 尝试直接按标准格式解析
	var st StdError
	if err := safeJSONParse(bodyText, &st); err == nil && st.Error.Code != 0 {
		if st.Error.Status == "" {
			st.Error.Status = statusToGoogleStatus(st.Error.Code)
		}
		return &st, resp.Header.Get("Retry-After")
	}
	// 构造标准错误
	code := resp.StatusCode
	msg := resp.Status
	if code == 429 {
		msg = "Resource has been exhausted (e.g. check quota)."
	}
	st = StdError{
		Error: StdErrorInner{
			Code:    code,
			Message: msg,
			Status:  statusToGoogleStatus(code),
		},
	}
	if bodyText != "" {
		st.Error.Details = []any{map[string]any{
			"@type":         "proxy.upstream",
			"upstream_error": truncate(bodyText, CONFIG.LogTruncationLimit),
		}}
	}
	return &st, resp.Header.Get("Retry-After")
}

func writeSSEErrorFromUpstream(w http.ResponseWriter, resp *http.Response) {
	std, ra := standardizeInitialError(resp)
	if ra != "" {
		std.Error.Details = append(std.Error.Details, map[string]any{
			"@type":       "proxy.retry",
			"retry_after": ra,
		})
	}
	b, _ := json.Marshal(std)
	writeSSEEvent(w, "error", string(b))
}

// -------------------- SSE 构件 --------------------

func writeSSEEvent(w http.ResponseWriter, event, data string) {
	flusher, _ := w.(http.Flusher)
	if event != "" {
		io.WriteString(w, "event: "+event+"\n")
	}
	// data 可能包含多行，逐行 data:
	for _, line := range strings.Split(data, "\n") {
		io.WriteString(w, "data: "+line+"\n")
	}
	io.WriteString(w, "\n")
	if flusher != nil {
		flusher.Flush()
	}
}

func writeRawSSELine(w http.ResponseWriter, line string) {
	// 与 JS 版本一致：直接把行写回去，并额外加一个空行
	flusher, _ := w.(http.Flusher)
	io.WriteString(w, line+"\n\n")
	if flusher != nil {
		flusher.Flush()
	}
}

// 迭代上游 SSE：以行为单位输出（允许超长行）
func sseLineIterator(r io.Reader) (<-chan string, <-chan error) {
	out := make(chan string)
	errc := make(chan error, 1)
	go func() {
		defer close(out)
		defer close(errc)

		sc := bufio.NewScanner(r)
		// 增大 buffer，避免长行被截断
		buf := make([]byte, 0, 256*1024)
		sc.Buffer(buf, 4*1024*1024)

		var buffer string
		for sc.Scan() {
			line := sc.Text()
			if line != "" {
				out <- line
			} else {
				// 保留空行给上层（为了更忠实转发），但这里与原实现相近，空行由 writeRawSSELine 统一产生
				_ = buffer
			}
		}
		if err := sc.Err(); err != nil {
			errc <- err
			return
		}
	}()
	return out, errc
}

// -------------------- 智能退避策略 --------------------

type retryRecord struct {
	Timestamp int64
	TextLen   int
	Progress  int
	Reason    string
}

type IntelligentBackoff struct {
	RetryHistory       []retryRecord
	LastTextLength     int
	NoProgressCount    int
	TruncationPositions []int
	CurrentDelay       int
}

func NewIntelligentBackoff() *IntelligentBackoff {
	return &IntelligentBackoff{
		RetryHistory:       make([]retryRecord, 0, 8),
		LastTextLength:     0,
		NoProgressCount:    0,
		TruncationPositions: []int{},
		CurrentDelay:       CONFIG.RetryDelayMs,
	}
}

func (b *IntelligentBackoff) RecordAttempt(textLength int, interruptionReason string) {
	progress := textLength - b.LastTextLength
	b.RetryHistory = append(b.RetryHistory, retryRecord{
		Timestamp: time.Now().UnixMilli(),
		TextLen:   textLength,
		Progress:  progress,
		Reason:    interruptionReason,
	})
	if progress < CONFIG.MinProgressChars {
		b.NoProgressCount++
		logWarn("No significant progress detected. Count: %d/%d", b.NoProgressCount, CONFIG.NoProgressThreshold)
	} else {
		b.NoProgressCount = 0
	}
	b.TruncationPositions = append(b.TruncationPositions, textLength)
	b.LastTextLength = textLength
}

func (b *IntelligentBackoff) IsLikelyContentIssue() bool {
	// 1) 连续多次无进展
	if b.NoProgressCount >= CONFIG.NoProgressThreshold {
		logError("Content issue detected: No progress for multiple retries")
		return true
	}
	// 2) 截断位置方差很小
	if len(b.TruncationPositions) >= 3 {
		last := b.TruncationPositions[len(b.TruncationPositions)-3:]
		minv, maxv := last[0], last[0]
		for _, v := range last {
			if v < minv {
				minv = v
			}
			if v > maxv {
				maxv = v
			}
		}
		if maxv-minv < 50 {
			logError("Content issue detected: Repeated truncation around ~%d chars", (minv+maxv)/2)
			return true
		}
	}
	// 3) 失败原因一致且为 STOP_WITHOUT_ANSWER
	if len(b.RetryHistory) >= 3 {
		r := b.RetryHistory[len(b.RetryHistory)-3:]
		eq := r[0].Reason == r[1].Reason && r[1].Reason == r[2].Reason
		if eq {
			logWarn("Repeated failure with same reason: %s", r[0].Reason)
			if r[0].Reason == "STOP_WITHOUT_ANSWER" {
				logError("Content issue detected: Consistent STOP without answer")
				return true
			}
		}
	}
	return false
}

func (b *IntelligentBackoff) NextDelay() int {
	if b.IsLikelyContentIssue() {
		return CONFIG.MaxRetryDelayMs
	}
	delay := int(math.Min(float64(b.CurrentDelay)*1.5, float64(CONFIG.MaxRetryDelayMs)))
	// 如果上一次进展很大，重置为初始延迟
	if len(b.RetryHistory) > 0 {
		last := b.RetryHistory[len(b.RetryHistory)-1]
		if last.Progress > 500 {
			b.CurrentDelay = CONFIG.RetryDelayMs
			return b.CurrentDelay
		}
	}
	b.CurrentDelay = delay
	return b.CurrentDelay
}

func (b *IntelligentBackoff) StrategyReport() map[string]any {
	avg := 0
	if len(b.RetryHistory) > 0 {
		sum := 0
		for _, h := range b.RetryHistory {
			sum += h.Progress
		}
		avg = int(math.Round(float64(sum) / float64(len(b.RetryHistory))))
	}
	// 只返回最后 5 个截断位置
	last5 := []int{}
	if len(b.TruncationPositions) <= 5 {
		last5 = append(last5, b.TruncationPositions...)
	} else {
		last5 = append(last5, b.TruncationPositions[len(b.TruncationPositions)-5:]...)
	}
	return map[string]any{
		"totalRetries":     len(b.RetryHistory),
		"noProgressCount":  b.NoProgressCount,
		"averageProgress":  avg,
		"isContentIssue":   b.IsLikelyContentIssue(),
		"currentDelay":     b.CurrentDelay,
		"truncationPattern": last5,
	}
}

// -------------------- 请求体构造（重试续写） --------------------

func buildRetryRequestBody(original any, accumulated string, isContentIssue bool) any {
	logDebug("Building retry request. Accumulated length: %d, ContentIssue: %v", len(accumulated), isContentIssue)

	accumulated = sanitizeTextForJSON(accumulated)
	logDebug("Sanitized text length: %d", len(accumulated))
	logDebug("Sanitized text preview: %s", truncate(accumulated, 500))

	// 深拷贝并清洗
	body := deepCleanClone(original)

	m, ok := body.(map[string]any)
	if !ok {
		// 回退到最小结构
		return map[string]any{
			"contents": []any{
				map[string]any{"role": "user", "parts": []any{map[string]any{"text": "Continue the previous response."}}},
			},
		}
	}
	contents, _ := m["contents"].([]any)
	if contents == nil {
		contents = []any{}
	}

	// 寻找最后一个 user 消息的索引
	lastUserIdx := -1
	for i := range contents {
		item, _ := contents[i].(map[string]any)
		if item != nil {
			if role, _ := item["role"].(string); role == "user" {
				lastUserIdx = i
			}
		}
	}
	continuePrompt := "Continue exactly where you left off, providing the final answer without repeating the previous thinking steps."
	if isContentIssue {
		continuePrompt = "The previous response was interrupted. Please provide a complete and final answer to the original question. If there were any issues with the previous attempt, please try a different approach."
		logWarn("Using alternative prompt due to detected content issue")
	}

	history := []any{
		map[string]any{
			"role": "model",
			"parts": []any{
				map[string]any{"text": accumulated},
			},
		},
		map[string]any{
			"role": "user",
			"parts": []any{
				map[string]any{"text": continuePrompt},
			},
		},
	}

	if lastUserIdx >= 0 {
		// 在最后一个 user 之后插入
		head := append([]any{}, contents[:lastUserIdx+1]...)
		tail := append([]any{}, contents[lastUserIdx+1:]...)
		contents = append(head, append(history, tail...)...)
	} else {
		contents = append(contents, history...)
	}
	m["contents"] = contents

	// 验证序列化
	if _, err := json.Marshal(m); err != nil {
		logError("Request body validation failed: %v", err)
		return map[string]any{
			"contents": []any{
				map[string]any{"role": "user", "parts": []any{map[string]any{"text": "Continue the previous response."}}},
			},
		}
	}
	logDebug("Constructed retry request body: %s", truncate(toJSONString(m), CONFIG.LogTruncationLimit))
	return m
}

func toJSONString(v any) string {
	b, _ := json.Marshal(v)
	return string(b)
}

// -------------------- 头部复制 --------------------

func buildUpstreamHeaders(src http.Header) http.Header {
	h := make(http.Header)
	copyHeader := func(k string) {
		if v := src.Get(k); v != "" {
			h.Set(k, v)
		}
	}
	copyHeader("Authorization")
	copyHeader("X-Goog-Api-Key")
	copyHeader("Content-Type")
	copyHeader("Accept")
	return h
}

// -------------------- SSE 数据结构（用于检测 finishReason/文本等） --------------------

type ssePayload struct {
	Candidates []struct {
		Content struct {
			Parts []struct {
				Text         string      `json:"text,omitempty"`
				Thought      *bool       `json:"thought,omitempty"`
				FunctionCall interface{} `json:"functionCall,omitempty"`
				ToolCode     interface{} `json:"toolCode,omitempty"`
			} `json:"parts"`
		} `json:"content"`
		FinishReason string `json:"finishReason,omitempty"`
	} `json:"candidates"`
}

// -------------------- 处理请求 --------------------

func handleOPTIONS(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Goog-Api-Key")
	w.WriteHeader(http.StatusNoContent)
}

func isStream(r *http.Request) bool {
	q := r.URL.Query()
	return strings.ToLower(q.Get("alt")) == "sse"
}

func buildUpstreamURL(r *http.Request) (string, error) {
	base := strings.TrimRight(CONFIG.UpstreamURLBase, "/")
	path := r.URL.Path
	query := r.URL.RawQuery
	u := base + path
	if query != "" {
		u += "?" + query
	}
	_, err := url.Parse(u)
	return u, err
}

func handleNonStreaming(w http.ResponseWriter, r *http.Request) {
	upstreamURL, err := buildUpstreamURL(r)
	if err != nil {
		jsonError(w, 500, "Internal URL error", err.Error())
		return
	}
	logInfo("=== NEW NON-STREAMING REQUEST: %s %s ===", r.Method, r.URL.String())

	var body io.Reader
	if r.Method == http.MethodPost && r.Body != nil {
		reqBodyBytes, _ := io.ReadAll(r.Body)
		_ = r.Body.Close()
		if len(reqBodyBytes) > 0 {
			var anyv any
			if err := safeJSONParse(string(reqBodyBytes), &anyv); err == nil {
				cleaned := deepCleanClone(anyv)
				b, _ := json.Marshal(cleaned)
				body = bytes.NewReader(b)
			} else {
				// 无法解析为 JSON 就原样透传
				body = bytes.NewReader(reqBodyBytes)
			}
		}
	}

	req, err := http.NewRequestWithContext(r.Context(), r.Method, upstreamURL, body)
	if err != nil {
		jsonError(w, 500, "Internal Error", err.Error())
		return
	}
	req.Header = buildUpstreamHeaders(r.Header)

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		jsonError(w, 502, "Bad Gateway", err.Error())
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		std, _ := standardizeInitialError(resp)
		w.Header().Set("Content-Type", "application/json; charset=utf-8")
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.WriteHeader(resp.StatusCode)
		_ = json.NewEncoder(w).Encode(std)
		return
}

	// 复制上游响应
	copyHeaderNoHop(w.Header(), resp.Header)
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.WriteHeader(resp.StatusCode)
	_, _ = io.Copy(w, resp.Body)
}

func copyHeaderNoHop(dst, src http.Header) {
	for k, vv := range src {
		// 跳过 hop-by-hop 头（简单处理）
		switch textproto.CanonicalMIMEHeaderKey(k) {
		case "Connection", "Keep-Alive", "Proxy-Authenticate", "Proxy-Authorization", "Te", "Trailers", "Transfer-Encoding", "Upgrade":
			continue
		}
		for _, v := range vv {
			dst.Add(k, v)
		}
	}
}

// 核心：Streaming + 内部重试
func handleStreamingPost(w http.ResponseWriter, r *http.Request) {
	upstreamURL, err := buildUpstreamURL(r)
	if err != nil {
		jsonError(w, 500, "Internal URL error", err.Error())
		return
	}
	logInfo("=== NEW STREAMING REQUEST: %s %s ===", r.Method, r.URL.String())

	// 读取并清洗原始请求体
	var originalBody any
	var cleanedBody any
	{
		reqTextBytes, _ := io.ReadAll(r.Body)
		_ = r.Body.Close()
		reqText := string(reqTextBytes)
		logInfo("Request body (raw, %d bytes): %s", len(reqText), truncate(reqText, CONFIG.LogTruncationLimit))
		if err := safeJSONParse(reqText, &originalBody); err != nil || originalBody == nil {
			jsonError(w, 400, "Invalid JSON in request body", errString(err))
			return
		}
		// 打印 contents 摘要
		if m, ok := originalBody.(map[string]any); ok {
			if arr, ok2 := m["contents"].([]any); ok2 {
				logInfo("Request contains %d messages:", len(arr))
				for i, it := range arr {
					if mm, ok3 := it.(map[string]any); ok3 {
						role, _ := mm["role"].(string)
						parts, _ := mm["parts"].([]any)
						var texts []string
						for _, p := range parts {
							if pm, ok4 := p.(map[string]any); ok4 {
								if ts, ok5 := pm["text"].(string); ok5 {
									texts = append(texts, sanitizeTextForJSON(ts))
								} else {
									texts = append(texts, "[non-text part]")
								}
							}
						}
						logInfo("  [%d] role=%s, text: %s", i, coalesce(role, "unknown"), truncate(strings.Join(texts, "\n"), 1000))
					}
				}
			}
		}
		cleanedBody = deepCleanClone(originalBody)
	}

	// 设置 SSE 响应头
	w.Header().Set("Content-Type", "text/event-stream; charset=utf-8")
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	
	flusher, _ := w.(http.Flusher)
	if flusher != nil {
		flusher.Flush()
	}

	ctx := r.Context()
	backoff := NewIntelligentBackoff()
	accumulatedText := strings.Builder{}
	consecutiveRetries := 0
	sessionStart := time.Now()

	// 发起一次请求并循环处理（含重试）
	makeUpstreamReq := func(body any) (*http.Response, error) {
		b, _ := json.Marshal(body)
		req, err := http.NewRequestWithContext(ctx, http.MethodPost, upstreamURL, bytes.NewReader(b))
		if err != nil {
			return nil, err
		}
		req.Header = buildUpstreamHeaders(r.Header)
		return http.DefaultClient.Do(req)
	}

	// 初始请求
	logInfo("=== MAKING INITIAL REQUEST TO UPSTREAM ===")
	initialResp, err := makeUpstreamReq(cleanedBody)
	if err != nil {
		jsonError(w, 502, "Bad Gateway", err.Error())
		return
	}
	logInfo("Initial upstream response received. Status: %d %s", initialResp.StatusCode, initialResp.Status)

	if initialResp.StatusCode < 200 || initialResp.StatusCode >= 300 {
		writeSSEErrorFromUpstream(w, initialResp)
		return
	}

	currentResp := initialResp
	defer func() {
		if currentResp != nil && currentResp.Body != nil {
			_ = currentResp.Body.Close()
		}
	}()

	for {
		attemptStart := time.Now()
		linesInThisStream := 0
		textInThisStream := 0
		reasoningStepDetected := false
		hasReceivedFinalAnswerContent := false
		var interruptionReason string
		finishReasonArrived := false

		logInfo("=== Starting stream attempt %d/%d ===", consecutiveRetries+1, CONFIG.MaxConsecutiveRetries+1)

		lineCh, errCh := sseLineIterator(currentResp.Body)
		for {
			select {
			case line, ok := <-lineCh:
				if !ok {
					lineCh = nil
				} else {
					linesInThisStream++
					writeRawSSELine(w, line) // 直接转发给客户端
					logDebug("SSE Line %d: %s", linesInThisStream, truncate(line, 500))

					if !strings.HasPrefix(line, "data: ") {
						continue
					}
					dataStr := strings.TrimSpace(strings.TrimPrefix(line, "data: "))
					if dataStr == "" {
						continue
					}
					var payload ssePayload
					if err := safeJSONParse(dataStr, &payload); err != nil {
						logDebug("Failed to parse SSE data line, skipping: %v", err)
						continue
					}
					if len(payload.Candidates) == 0 {
						continue
					}
					c := payload.Candidates[0]
					// parts
					for _, p := range c.Content.Parts {
						if p.Text != "" {
							ct := sanitizeTextForJSON(p.Text)
							accumulatedText.WriteString(ct)
							textInThisStream += len(ct)
							if p.Thought == nil || (p.Thought != nil && *p.Thought == false) {
								hasReceivedFinalAnswerContent = true
								logDebug("Received final answer content (non-thought part).")
							} else {
								logDebug("Received 'thought' content part.")
							}
						} else if p.FunctionCall != nil || p.ToolCode != nil {
							reasoningStepDetected = true
							logInfo("Reasoning step detected (tool/function call)")
						}
					}
					// finish reason
					if c.FinishReason != "" {
						finishReasonArrived = true
						logInfo("Finish reason received: %s", c.FinishReason)
						switch c.FinishReason {
						case "STOP":
							if hasReceivedFinalAnswerContent {
								sessionDuration := time.Since(sessionStart)
								logInfo("=== STREAM COMPLETED SUCCESSFULLY (Reason: STOP after final answer) ===")
								logInfo("  - Total session duration: %v, Retries: %d", sessionDuration, consecutiveRetries)
								logInfo("  - Strategy Report: %s", toJSONString(backoff.StrategyReport()))
								return
							}
							logError("Stream finished with STOP but no final answer content was received.")
							interruptionReason = "STOP_WITHOUT_ANSWER"
						case "MAX_TOKENS", "TOOL_CODE", "SAFETY", "RECITATION":
							logInfo("Stream terminated with reason: %s. Closing stream.", c.FinishReason)
							return
						default:
							logError("Abnormal/unknown finish reason: %s", c.FinishReason)
							interruptionReason = "FINISH_ABNORMAL"
						}
					}
					if interruptionReason != "" {
						break
					}
				}
			case err := <-errCh:
				if err != nil && !errors.Is(err, io.EOF) {
					logError("Exception during stream processing: %v", err)
					if reasoningStepDetected {
						interruptionReason = "DROP_DURING_REASONING"
					} else {
						interruptionReason = "DROP"
					}
				}
				errCh = nil
			}
			if lineCh == nil && errCh == nil {
				// 上游流自然结束且无 finishReason
				if interruptionReason == "" && !finishReasonArrived {
					logError("Stream ended prematurely without a finish reason (DROP).")
					if reasoningStepDetected {
						interruptionReason = "DROP_DURING_REASONING"
					} else {
						interruptionReason = "DROP"
					}
				}
				break
			}
		}

		// 清理当前 resp.Body（下一轮重试需要新请求）
		if currentResp != nil && currentResp.Body != nil {
			_ = currentResp.Body.Close()
		}

		logInfo("Stream attempt %d summary: Duration: %v, Lines: %d, Chars: %d, Total Chars: %d",
			consecutiveRetries+1, time.Since(attemptStart), linesInThisStream, textInThisStream, accumulatedText.Len(),
		)

		if interruptionReason == "" {
			logInfo("Stream finished without interruption. Closing.")
			return
		}
		logError("=== STREAM INTERRUPTED (Reason: %s) ===", interruptionReason)

		// 记录重试
		backoff.RecordAttempt(accumulatedText.Len(), interruptionReason)

		isContentIssue := backoff.IsLikelyContentIssue()
		if isContentIssue && consecutiveRetries >= 5 {
			logError("Content issue persists after 5 retries. Terminating early.")
			payload := StdError{
				Error: StdErrorInner{
					Code:    504,
					Status:  "DEADLINE_EXCEEDED",
					Message: fmt.Sprintf("Content issue detected: The response cannot be completed due to potential content limitations. Retried %d times.", consecutiveRetries),
					Details: []any{map[string]any{
						"@type":           "proxy.content_issue",
						"strategy_report": backoff.StrategyReport(),
					}},
				},
			}
			b, _ := json.Marshal(payload)
			writeSSEEvent(w, "error", string(b))
			return
		}

		if consecutiveRetries >= CONFIG.MaxConsecutiveRetries {
			logError("Retry limit exceeded. Sending final error to client.")
			payload := StdError{
				Error: StdErrorInner{
					Code:    504,
					Status:  "DEADLINE_EXCEEDED",
					Message: fmt.Sprintf("Proxy retry limit (%d) exceeded. Last interruption: %s.", CONFIG.MaxConsecutiveRetries, interruptionReason),
					Details: []any{map[string]any{
						"@type":           "proxy.retry_exhausted",
						"strategy_report": backoff.StrategyReport(),
					}},
				},
			}
			b, _ := json.Marshal(payload)
			writeSSEEvent(w, "error", string(b))
			return
		}

		consecutiveRetries++
		logInfo("Proceeding to retry attempt %d...", consecutiveRetries)

		// 智能退避
		delay := backoff.NextDelay()
		if delay > 0 {
			logInfo("⏳ Waiting %dms before retrying (intelligent backoff)...", delay)
			select {
			case <-time.After(time.Duration(delay) * time.Millisecond):
			case <-ctx.Done():
				return
			}
		}

		retryBody := buildRetryRequestBody(cleanedBody, accumulatedText.String(), isContentIssue)
		resp, err := makeUpstreamReq(retryBody)
		if err != nil {
			logError("Exception during retry setup: %v", err)
			// 继续循环，可能超出重试上限后退出
			continue
		}
		logInfo("Retry request completed. Status: %d %s", resp.StatusCode, resp.Status)
		if _, bad := nonRetryableStatuses[resp.StatusCode]; bad {
			logError("FATAL: Received non-retryable status %d during retry.", resp.StatusCode)
			writeSSEErrorFromUpstream(w, resp)
			return
		}
		if resp.StatusCode < 200 || resp.StatusCode >= 300 || resp.Body == nil {
			_ = resp.Body.Close()
			logError("Upstream server error on retry: %d", resp.StatusCode)
			// 继续下一轮，会因重试上限而退出
			continue
		}
		currentResp = resp
		logInfo("✓ Retry successful. Got new stream.")
	}
}

func errString(err error) string {
	if err == nil {
		return ""
	}
	return err.Error()
}

func coalesce[T comparable](v, def T) T {
	var zero T
	if v == zero {
		return def
	}
	return v
}

// -------------------- 入口与路由 --------------------

func main() {
	mux := http.NewServeMux()
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		defer func() {
			if rec := recover(); rec != nil {
				logError("!!! TOP-LEVEL PANIC !!! %v", rec)
				jsonError(w, 500, "Internal Server Error", "The proxy worker encountered a critical error.")
			}
		}()

		// CORS 预检
		if r.Method == http.MethodOptions {
			handleOPTIONS(w, r)
			return
		}

		// 根据 alt=sse + POST 判断走流式或普通代理
		if r.Method == http.MethodPost && isStream(r) {
			handleStreamingPost(w, r)
			return
		}
		handleNonStreaming(w, r)
	})

	addr := ":" + CONFIG.Port
	srv := &http.Server{
		Addr:              addr,
		Handler:           logMiddleware(mux),
		ReadHeaderTimeout: 15 * time.Second,
	}
	logInfo("Starting server on %s (Upstream: %s)", addr, CONFIG.UpstreamURLBase)
	if err := srv.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
		log.Fatalf("server error: %v", err)
	}
}

func logMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()
		ww := &wrapWriter{ResponseWriter: w, status: 200}
		next.ServeHTTP(ww, r)
		logInfo("%s %s -> %d (%v)", r.Method, r.URL.String(), ww.status, time.Since(start))
	})
}

type wrapWriter struct {
	http.ResponseWriter
	status int
}

func (w *wrapWriter) WriteHeader(code int) {
	w.status = code
	w.ResponseWriter.WriteHeader(code)
}

// -------------------- 可选：优雅关闭（未启用） --------------------
// 需要可选地在收到信号时关闭：
// func gracefulShutdown(srv *http.Server) {
// 	ch := make(chan os.Signal, 1)
// 	signal.Notify(ch, os.Interrupt, syscall.SIGTERM)
// 	<-ch
// 	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
// 	defer cancel()
// 	_ = srv.Shutdown(ctx)
// }

// -------------------- 结束 --------------------
