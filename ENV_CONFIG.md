# 环境变量配置

## 可用配置项

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `PORT` | 8080 | 监听端口 |
| `UPSTREAM_URL_BASE` | https://api-proxy.me/gemini | 上游API地址 |
| `MAX_CONSECUTIVE_RETRIES` | 10 | 最大重试次数 |
| `DEBUG_MODE` | true | 调试模式 (true/false) |
| `RETRY_DELAY_MS` | 750 | 重试延迟(毫秒) |
| `LOG_TRUNCATION_LIMIT` | 8000 | 日志截断长度 |

## Docker运行

### 快速启动
```bash
docker run -p 8080:8080 curaalizm/gemini-antiblock
```

### 自定义配置
```bash
docker run -p 9090:9090 \
  -e PORT=9090 \
  -e DEBUG_MODE=false \
  -e MAX_CONSECUTIVE_RETRIES=5 \
  curaalizm/gemini-antiblock
```

### Docker Compose
```bash
docker-compose up -d
```

### 构建本地镜像
```bash
# 构建
docker build -t gemini-antiblock .

# 运行
docker run -p 8080:8080 gemini-antiblock
```