@echo off
REM Docker Hub发布脚本 (Windows版本)
REM 使用方法: publish.bat [version]
REM 示例: publish.bat v1.0.0

setlocal

REM 配置
set DOCKER_USERNAME=curaalizm
set IMAGE_NAME=gemini-antiblock
if "%1"=="" (
    set VERSION=latest
) else (
    set VERSION=%1
)

echo 🚀 开始构建和发布 Docker 镜像...
echo 用户名: %DOCKER_USERNAME%
echo 镜像名: %IMAGE_NAME%
echo 版本: %VERSION%
echo.

REM 检查Docker是否运行
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker 未运行，请先启动 Docker Desktop
    pause
    exit /b 1
)

REM 检查是否已登录Docker Hub
echo 检查 Docker Hub 登录状态...
for /f "tokens=*" %%i in ('docker info 2^>^&1 ^| findstr "Username"') do set DOCKER_INFO=%%i
echo %DOCKER_INFO% | findstr /C:"%DOCKER_USERNAME%" >nul
if errorlevel 1 (
    echo ❌ 请先登录 Docker Hub: docker login
    pause
    exit /b 1
)

REM 构建镜像
echo 🔨 构建镜像...
docker build -t %DOCKER_USERNAME%/%IMAGE_NAME%:%VERSION% .
if errorlevel 1 (
    echo ❌ 镜像构建失败
    pause
    exit /b 1
)

docker build -t %DOCKER_USERNAME%/%IMAGE_NAME%:latest .
if errorlevel 1 (
    echo ❌ latest标签构建失败
    pause
    exit /b 1
)

REM 推送镜像
echo 📤 推送镜像到 Docker Hub...
docker push %DOCKER_USERNAME%/%IMAGE_NAME%:%VERSION%
if errorlevel 1 (
    echo ❌ 版本镜像推送失败
    pause
    exit /b 1
)

docker push %DOCKER_USERNAME%/%IMAGE_NAME%:latest
if errorlevel 1 (
    echo ❌ latest镜像推送失败
    pause
    exit /b 1
)

echo.
echo ✅ 发布完成！
echo 镜像地址: https://hub.docker.com/r/%DOCKER_USERNAME%/%IMAGE_NAME%
echo.
echo 💡 提示：为了构建多架构镜像（AMD64、ARM64、ARM/v7），请使用以下方法之一：
echo 1. 在Linux/macOS环境下运行 build-multi-arch.sh 脚本
echo 2. 在Windows环境下运行 docker-buildx.ps1 脚本（需要PowerShell）
echo 3. 使用Docker Buildx手动构建
echo.
echo 拉取命令: docker pull %DOCKER_USERNAME%/%IMAGE_NAME%:%VERSION%
pause