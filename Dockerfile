# 构建阶段
FROM --platform=$BUILDPLATFORM golang:1.22-alpine AS builder

ARG TARGETOS
ARG TARGETARCH

WORKDIR /app
COPY . .

# 静态编译支持多架构并优化二进制大小
RUN CGO_ENABLED=0 GOOS=$TARGETOS GOARCH=$TARGETARCH go build -a -installsuffix cgo -ldflags '-s -w' -o main .

# 运行阶段
FROM scratch

# 安装CA证书
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

# 从构建阶段复制编译好的二进制文件
COPY --from=builder /app/main /main

EXPOSE 8080

ENTRYPOINT ["/main"]