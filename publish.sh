#!/bin/bash

# Docker Hub发布脚本
# 使用方法: ./publish.sh [version]
# 示例: ./publish.sh v1.0.0

set -e

# 配置
DOCKER_USERNAME="curaalizm"
IMAGE_NAME="gemini-antiblock"
VERSION=${1:-"latest"}

echo "🚀 开始构建和发布 Docker 镜像..."
echo "用户名: $DOCKER_USERNAME"
echo "镜像名: $IMAGE_NAME"
echo "版本: $VERSION"
echo ""

# 检查是否已登录Docker Hub
if ! docker info | grep -q "Username: $DOCKER_USERNAME"; then
    echo "📝 请先登录 Docker Hub:"
    docker login
fi

# 检查是否安装了Docker Buildx
if docker buildx version > /dev/null 2>&1; then
    echo "🔧 检测到 Docker Buildx，使用多架构构建..."
    
    # 创建并使用构建器实例
    echo "🔨 创建构建器实例..."
    BUILDER_NAME="mybuilder-$(date +%s)"
    docker buildx create --name $BUILDER_NAME --use
    
    # 确保构建器启动
    docker buildx inspect --bootstrap
    
    # 构建并推送多架构镜像
    echo "🏗️  构建并推送多架构镜像..."
    docker buildx build --platform linux/amd64,linux/arm64,linux/arm/v7 \
        -t $DOCKER_USERNAME/$IMAGE_NAME:$VERSION \
        -t $DOCKER_USERNAME/$IMAGE_NAME:latest \
        . --push
    
    # 清理构建器实例
    echo "🧹 清理构建器实例..."
    docker buildx rm $BUILDER_NAME
else
    echo "🔧 未检测到 Docker Buildx，使用传统构建..."
    
    # 构建镜像
    echo "🔨 构建镜像..."
    docker build -t $DOCKER_USERNAME/$IMAGE_NAME:$VERSION .
    docker build -t $DOCKER_USERNAME/$IMAGE_NAME:latest .
    
    # 推送镜像
    echo "📤 推送镜像到 Docker Hub..."
    docker push $DOCKER_USERNAME/$IMAGE_NAME:$VERSION
    docker push $DOCKER_USERNAME/$IMAGE_NAME:latest
fi

echo ""
echo "✅ 发布完成！"
echo "镜像地址: https://hub.docker.com/r/$DOCKER_USERNAME/$IMAGE_NAME"
if docker buildx version > /dev/null 2>&1; then
    echo "支持架构: linux/amd64, linux/arm64, linux/arm/v7"
fi
echo "拉取命令: docker pull $DOCKER_USERNAME/$IMAGE_NAME:$VERSION"