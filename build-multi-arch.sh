#!/bin/bash

# 多架构构建脚本
# 使用方法: ./build-multi-arch.sh [version]
# 示例: ./build-multi-arch.sh v1.0.0

set -e

# 配置
DOCKER_USERNAME="curaalizm"
IMAGE_NAME="gemini-antiblock"
VERSION=${1:-"latest"}

echo "🚀 开始构建和发布多架构 Docker 镜像..."
echo "用户名: $DOCKER_USERNAME"
echo "镜像名: $IMAGE_NAME"
echo "版本: $VERSION"
echo ""

# 检查是否安装了Docker Buildx
if ! docker buildx version > /dev/null 2>&1; then
    echo "❌ 未安装 Docker Buildx"
    echo "请先安装 Docker Buildx: https://github.com/docker/buildx#installing"
    exit 1
fi

# 检查是否已登录Docker Hub
if ! docker info | grep -q "Username: $DOCKER_USERNAME"; then
    echo "📝 请先登录 Docker Hub:"
    docker login
fi

# 创建并使用构建器实例
echo "🔨 创建构建器实例..."
BUILDER_NAME="mybuilder-$(date +%s)"
docker buildx create --name $BUILDER_NAME --use

# 确保构建器启动
docker buildx inspect --bootstrap

# 构建并推送多架构镜像
echo "🏗️  构建并推送多架构镜像..."
docker buildx build --platform linux/amd64,linux/arm64,linux/arm/v7 \
    -t $DOCKER_USERNAME/$IMAGE_NAME:$VERSION \
    -t $DOCKER_USERNAME/$IMAGE_NAME:latest \
    . --push

# 清理构建器实例
echo "🧹 清理构建器实例..."
docker buildx rm $BUILDER_NAME

echo ""
echo "✅ 多架构镜像发布完成！"
echo "镜像地址: https://hub.docker.com/r/$DOCKER_USERNAME/$IMAGE_NAME"
echo "支持架构: linux/amd64, linux/arm64, linux/arm/v7"
echo "拉取命令: docker pull $DOCKER_USERNAME/$IMAGE_NAME:$VERSION"